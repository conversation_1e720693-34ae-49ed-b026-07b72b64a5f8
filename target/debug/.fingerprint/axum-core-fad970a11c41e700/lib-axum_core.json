{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 9791267232460094210, "path": 12675119491120528943, "deps": [[784494742817713399, "tower_service", false, 359651755574788416], [1906322745568073236, "pin_project_lite", false, 5128463840950300641], [2517136641825875337, "sync_wrapper", false, 15869315996447057178], [7620660491849607393, "futures_core", false, 15089605434749257820], [7712452662827335977, "tower_layer", false, 14028769166471425957], [7858942147296547339, "rustversion", false, 10535099639334957429], [9010263965687315507, "http", false, 5614802736293100029], [10229185211513642314, "mime", false, 7918414244013462605], [14084095096285906100, "http_body", false, 13895771565726926796], [16066129441945555748, "bytes", false, 3069146400235433948], [16900715236047033623, "http_body_util", false, 17596348138962680706]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-core-fad970a11c41e700/dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}