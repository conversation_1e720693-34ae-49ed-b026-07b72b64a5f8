{"rustc": 15497389221046826682, "features": "[\"attributes\", \"default\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 1006155289083248400, "path": 12040546271224300493, "deps": [[1906322745568073236, "pin_project_lite", false, 5128463840950300641], [2967683870285097694, "tracing_attributes", false, 8577435697104811083], [11033263105862272874, "tracing_core", false, 6564179413452067103]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-1b63c047a56b68c4/dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}