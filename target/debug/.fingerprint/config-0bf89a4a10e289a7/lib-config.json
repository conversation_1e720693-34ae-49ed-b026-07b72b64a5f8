{"rustc": 15497389221046826682, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 1398949370558892206, "path": 10162044332273838614, "deps": [[1213098572879462490, "json5_rs", false, 16808220734078218820], [1238778183371849706, "yaml_rust2", false, 7619562824171708444], [2244620803250265856, "ron", false, 18186005052961949573], [2356429411733741858, "ini", false, 7627912881497434494], [6517602928339163454, "path<PERSON><PERSON>", false, 16167932994736035655], [8786711029710048183, "toml", false, 13228373225236269757], [9689903380558560274, "serde", false, 955222618362440181], [11946729385090170470, "async_trait", false, 9620414555846116359], [13475460906694513802, "convert_case", false, 10963287255713169168], [14718834678227948963, "winnow", false, 10371683915642526214], [15367738274754116744, "serde_json", false, 2117296604063780219]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/config-0bf89a4a10e289a7/dep-lib-config", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}