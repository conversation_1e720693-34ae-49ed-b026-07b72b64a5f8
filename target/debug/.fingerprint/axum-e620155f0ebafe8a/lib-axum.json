{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"__private_docs\", \"default\", \"form\", \"headers\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 12074263998246110377, "profile": 5347358027863023418, "path": 2695021009374120339, "deps": [[40386456601120721, "percent_encoding", false, 755563419571576490], [264090853244900308, "sync_wrapper", false, 15085964006181916794], [784494742817713399, "tower_service", false, 359651755574788416], [1906322745568073236, "pin_project_lite", false, 5128463840950300641], [3129130049864710036, "memchr", false, 9215593028095207485], [3601586811267292532, "tower", false, 12589801004461220381], [4405182208873388884, "http", false, 3058231608177343884], [7414427314941361239, "hyper", false, 12707550241736726354], [7695812897323945497, "itoa", false, 2713419439950949953], [7712452662827335977, "tower_layer", false, 14028769166471425957], [8915503303801890683, "http_body", false, 330512385060578120], [9293824762099617471, "axum_core", false, 14014787265020073612], [9678799920983747518, "matchit", false, 6817934292515116471], [9689903380558560274, "serde", false, 10136608106485989546], [10229185211513642314, "mime", false, 7918414244013462605], [10435729446543529114, "bitflags", false, 14506194519610287483], [10629569228670356391, "futures_util", false, 5260472400754369313], [11946729385090170470, "async_trait", false, 7172890008290943547], [16066129441945555748, "bytes", false, 3069146400235433948], [16244562316228021087, "build_script_build", false, 5057936745581714015]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-e620155f0ebafe8a/dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}