{"rustc": 15497389221046826682, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 3992724396554112236, "path": 17770270771451006161, "deps": [[1009387600818341822, "matchers", false, 16664267501119709803], [1017461770342116999, "sharded_slab", false, 4874178665204008925], [3722963349756955755, "once_cell", false, 16708681008072285910], [6048213226671835012, "smallvec", false, 17123784092870331895], [6981130804689348050, "tracing_serde", false, 4505066177044728721], [8606274917505247608, "tracing", false, 11232536847811313231], [8614575489689151157, "nu_ansi_term", false, 23276612335800068], [9451456094439810778, "regex", false, 4036360408020974509], [9689903380558560274, "serde", false, 955222618362440181], [10806489435541507125, "tracing_log", false, 6099717098234993561], [11033263105862272874, "tracing_core", false, 2561402944952176981], [12409575957772518135, "time", false, 3432887480635150245], [12427285511609802057, "thread_local", false, 4307798599230929668], [15367738274754116744, "serde_json", false, 2117296604063780219]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-subscriber-ca2236726b620ed8/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}