{"rustc": 15497389221046826682, "features": "[\"default-hasher\", \"inline-more\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 5347358027863023418, "path": 3325738606165159217, "deps": [[10842263908529601448, "<PERSON><PERSON><PERSON>", false, 210365465816349724]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-18b41527dd987741/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}