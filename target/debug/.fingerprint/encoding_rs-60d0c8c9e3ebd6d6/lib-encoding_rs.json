{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"any_all_workaround\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"serde\", \"simd-accel\"]", "target": 17616512236202378241, "profile": 5347358027863023418, "path": 17640807469601094085, "deps": [[10411997081178400487, "cfg_if", false, 33228885202160203]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/encoding_rs-60d0c8c9e3ebd6d6/dep-lib-encoding_rs", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}