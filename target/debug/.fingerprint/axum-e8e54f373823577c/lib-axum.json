{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"__private\", \"__private_docs\", \"default\", \"form\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 13920321295547257648, "profile": 9791267232460094210, "path": 17301405828534346599, "deps": [[40386456601120721, "percent_encoding", false, 755563419571576490], [784494742817713399, "tower_service", false, 359651755574788416], [1906322745568073236, "pin_project_lite", false, 5128463840950300641], [2517136641825875337, "sync_wrapper", false, 15869315996447057178], [3129130049864710036, "memchr", false, 9215593028095207485], [5695049318159433696, "tower", false, 16229808832293254700], [7695812897323945497, "itoa", false, 2713419439950949953], [7712452662827335977, "tower_layer", false, 14028769166471425957], [7858942147296547339, "rustversion", false, 10535099639334957429], [8913795983780778928, "matchit", false, 11558428077185336567], [9010263965687315507, "http", false, 5614802736293100029], [9689903380558560274, "serde", false, 10136608106485989546], [10229185211513642314, "mime", false, 7918414244013462605], [10629569228670356391, "futures_util", false, 5260472400754369313], [14084095096285906100, "http_body", false, 13895771565726926796], [15176407853393882315, "axum_core", false, 12259200189181138877], [16066129441945555748, "bytes", false, 3069146400235433948], [16900715236047033623, "http_body_util", false, 17596348138962680706]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-e8e54f373823577c/dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}