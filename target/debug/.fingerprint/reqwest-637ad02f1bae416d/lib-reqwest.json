{"rustc": 15497389221046826682, "features": "[\"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"deflate\", \"gzip\", \"hyper-rustls\", \"json\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"tokio-rustls\", \"tokio-util\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 5347358027863023418, "path": 12360358679216226155, "deps": [[40386456601120721, "percent_encoding", false, 755563419571576490], [95042085696191081, "ipnet", false, 173512168240873331], [126872836426101300, "async_compression", false, 11419058750140462525], [264090853244900308, "sync_wrapper", false, 15085964006181916794], [784494742817713399, "tower_service", false, 359651755574788416], [1044435446100926395, "hyper_rustls", false, 8818355837204838956], [1288403060204016458, "tokio_util", false, 2883154759175716073], [1906322745568073236, "pin_project_lite", false, 5128463840950300641], [3150220818285335163, "url", false, 5705893056082236043], [3722963349756955755, "once_cell", false, 16668147234706293438], [4405182208873388884, "http", false, 3058231608177343884], [5986029879202738730, "log", false, 9894818016212002977], [7414427314941361239, "hyper", false, 12707550241736726354], [7620660491849607393, "futures_core", false, 15089605434749257820], [8915503303801890683, "http_body", false, 330512385060578120], [9538054652646069845, "tokio", false, 12490149117820131034], [9689903380558560274, "serde", false, 10136608106485989546], [10229185211513642314, "mime", false, 7918414244013462605], [10629569228670356391, "futures_util", false, 5260472400754369313], [11107720164717273507, "system_configuration", false, 3003093804824154538], [11295624341523567602, "rustls", false, 9325160619046780784], [13809605890706463735, "h2", false, 9283846038615216320], [14564311161534545801, "encoding_rs", false, 3306559478793207057], [15367738274754116744, "serde_json", false, 15728504084392976648], [16066129441945555748, "bytes", false, 3069146400235433948], [16311359161338405624, "rustls_pemfile", false, 14317262461479795778], [16542808166767769916, "serde_urlencoded", false, 9584462491836187225], [16622232390123975175, "tokio_rustls", false, 13268831768483660409], [17652733826348741533, "webpki_roots", false, 13143308959562273141], [18066890886671768183, "base64", false, 10346665594967511270]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/reqwest-637ad02f1bae416d/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}