{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 3165595516910038244, "profile": 5347358027863023418, "path": 14698983297451476235, "deps": [[784494742817713399, "tower_service", false, 359651755574788416], [4405182208873388884, "http", false, 3058231608177343884], [7712452662827335977, "tower_layer", false, 14028769166471425957], [8915503303801890683, "http_body", false, 330512385060578120], [9293824762099617471, "build_script_build", false, 5976779945040639529], [10229185211513642314, "mime", false, 7918414244013462605], [10629569228670356391, "futures_util", false, 5260472400754369313], [11946729385090170470, "async_trait", false, 7172890008290943547], [16066129441945555748, "bytes", false, 3069146400235433948]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-core-6512032371797858/dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}