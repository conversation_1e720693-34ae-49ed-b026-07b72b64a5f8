{"rustc": 15497389221046826682, "features": "[\"alloc\", \"ansi\", \"default\", \"fmt\", \"nu-ansi-term\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 3992724396554112236, "path": 17770270771451006161, "deps": [[1017461770342116999, "sharded_slab", false, 4874178665204008925], [6048213226671835012, "smallvec", false, 17123784092870331895], [8614575489689151157, "nu_ansi_term", false, 23276612335800068], [10806489435541507125, "tracing_log", false, 6099717098234993561], [11033263105862272874, "tracing_core", false, 2561402944952176981], [12427285511609802057, "thread_local", false, 4307798599230929668]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-subscriber-a9ecefac60c7e6a9/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}