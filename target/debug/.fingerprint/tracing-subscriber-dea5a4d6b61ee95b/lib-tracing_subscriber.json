{"rustc": 15497389221046826682, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 1006155289083248400, "path": 17770270771451006161, "deps": [[1009387600818341822, "matchers", false, 10530335456354948628], [1017461770342116999, "sharded_slab", false, 9599486161422876994], [3722963349756955755, "once_cell", false, 16668147234706293438], [6048213226671835012, "smallvec", false, 12944421000608339639], [6981130804689348050, "tracing_serde", false, 5812210150577706255], [8606274917505247608, "tracing", false, 18233996573951143871], [8614575489689151157, "nu_ansi_term", false, 9521404238552385955], [9451456094439810778, "regex", false, 395951656299654614], [9689903380558560274, "serde", false, 299887857576531451], [10806489435541507125, "tracing_log", false, 4528138531855147232], [11033263105862272874, "tracing_core", false, 6564179413452067103], [12409575957772518135, "time", false, 16308834844053405391], [12427285511609802057, "thread_local", false, 15615115016700618564], [15367738274754116744, "serde_json", false, 16945641789552845665]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-subscriber-dea5a4d6b61ee95b/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}