{"rustc": 15497389221046826682, "features": "[\"default\"]", "declared_features": "[\"asm\", \"default\", \"parallel\", \"rayon\", \"std\"]", "target": 4360302069253712615, "profile": 5347358027863023418, "path": 2649503952403297065, "deps": [[477150410136574819, "ark_ff_macros", false, 12582039530403867593], [2932480923465029663, "zeroize", false, 165378283441228824], [5157631553186200874, "num_traits", false, 17436355615779580601], [11903278875415370753, "itertools", false, 8814090374611473052], [12528732512569713347, "num_bigint", false, 2496956683962157817], [13859769749131231458, "derivative", false, 3377065719756237067], [15179503056858879355, "ark_std", false, 13328564753060625683], [16925068697324277505, "ark_serialize", false, 17510048939506934275], [17475753849556516473, "digest", false, 3552290121158058625], [17605717126308396068, "paste", false, 7327368740265770451], [17996237327373919127, "ark_ff_asm", false, 7136705268148348958]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ark-ff-b7e363648d669dc7/dep-lib-ark_ff", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}