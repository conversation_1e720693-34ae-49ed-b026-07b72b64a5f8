{"rustc": 15497389221046826682, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode-case\", \"unicode-perl\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 5347358027863023418, "path": 15033791335116528145, "deps": [[555019317135488525, "regex_automata", false, 13348879194387176401], [2779309023524819297, "aho_corasick", false, 9657564163971662069], [3129130049864710036, "memchr", false, 9215593028095207485], [9408802513701742484, "regex_syntax", false, 3380114828820497717]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-1837eb4d8e2bfd13/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}