{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 18007145932999333080, "path": 3622169010033162278, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/winnow-f46126f8c95da648/dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}