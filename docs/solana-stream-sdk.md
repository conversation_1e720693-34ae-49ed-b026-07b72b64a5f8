# Solana Stream SDK - AI Coding Assistant Reference

## Overview

**Solana Stream SDK** is a comprehensive Rust library for streaming real-time Solana blockchain data using Ji<PERSON>'s Shredstream protocol. This document provides essential context for AI coding assistants working with shred decoding and Solana data streaming.

**Key Information:**

-   **Repository**: https://github.com/ValidatorsDAO/solana-stream
-   **Rust Crate**: `solana-stream-sdk` v0.2.5
-   **License**: Apache-2.0
-   **Purpose**: Stream Solana shreds for low-latency blockchain data access

## Source Code Analysis

### Repository Information

**Primary Repository**: [ValidatorsDAO/solana-stream](https://github.com/ValidatorsDAO/solana-stream)

**Complete Source Code**: Available in [`repomix-output-ValidatorsDAO-solana-stream.xml`](./repomix-output-ValidatorsDAO-solana-stream.xml)

### Code Structure Overview

The Solana Stream SDK is organized as a Rust workspace with multiple components:

**Workspace Structure:**

-   **`crate/solana-stream-sdk/`** - Main SDK implementation
-   **`client/`** - Multiple client examples and implementations
    -   `client/shreds-rs/` - Rust shred client example
    -   `client/geyser-rs/` - Rust Geyser client
    -   `client/geyser-ts/` - TypeScript Geyser client
-   **`package/`** - Additional utilities and packages

**Core SDK Modules (`crate/solana-stream-sdk/src/`):**

1. **`lib.rs`** - Main library exports and public API
2. **`error.rs`** - Error types and handling
3. **`shredstream.rs`** - Core ShredStream client implementation

**Protocol Definitions:**

-   **`shared.proto`** - Shared protobuf definitions
-   **`shredstream.proto`** - ShredStream-specific protobuf messages

### Key Implementation Details

-   **Language**: Rust with async/await using Tokio
-   **Protocol**: gRPC for client-server communication using Tonic
-   **Architecture**: Simple gRPC client wrapper with protobuf integration
-   **Key Dependencies**:
    -   Tonic gRPC framework
    -   Solana SDK for blockchain primitives
    -   Protobuf for message serialization
    -   Tokio for async runtime

### How to Read the Source Code

For AI assistants working with this SDK, start with these key files in order:

1. **`crate/solana-stream-sdk/src/lib.rs`** - Understand public API and main exports
2. **`crate/solana-stream-sdk/src/shredstream.rs`** - Core client implementation
3. **`crate/solana-stream-sdk/src/error.rs`** - Error handling patterns
4. **`client/shreds-rs/src/main.rs`** - Basic usage example
5. **`client/geyser-rs/src/main.rs`** - Advanced streaming example

**Key Patterns to Understand:**

-   gRPC streaming with Tonic
-   Protobuf message handling
-   Async stream processing with tokio_stream
-   Account and program filtering mechanisms

## Core Architecture

### ShredstreamClient (Primary Interface)

```rust
pub struct ShredstreamClient {
    client: ShredstreamProxyClient<Channel>,
}

impl ShredstreamClient {
    // Connect to shredstream endpoint
    pub async fn connect(endpoint: impl AsRef<str>) -> Result<Self>

    // Subscribe to entries stream with filters
    pub async fn subscribe_entries(
        &mut self,
        request: SubscribeEntriesRequest
    ) -> Result<tonic::Streaming<Entry>>

    // Helper methods for creating requests
    pub fn create_entries_request_for_account(
        account: impl AsRef<str>,
        commitment: Option<CommitmentLevel>
    ) -> SubscribeEntriesRequest

    pub fn create_entries_request_for_accounts(
        accounts: Vec<String>,
        owners: Vec<String>,
        filters: Vec<SubscribeRequestFilterAccountsFilter>,
        commitment: Option<CommitmentLevel>
    ) -> SubscribeEntriesRequest
}
```

### Key Data Structures

#### Entry Structure

```rust
message Entry {
    uint64 slot = 1;                                    // Slot number
    bytes entries = 2;                                  // Serialized Vec<solana_entry::entry::Entry>
}
```

#### Solana Entry (after deserialization)

```rust
pub struct Entry {
    pub num_hashes: u64,
    pub hash: Hash,
    pub transactions: Vec<VersionedTransaction>,
}
```

#### Commitment Levels

```rust
enum CommitmentLevel {
    PROCESSED = 0,  // Latest block processed by validator (fastest)
    CONFIRMED = 1,  // Block confirmed by cluster
    FINALIZED = 2,  // Block finalized by cluster (slowest)
}
```

## Essential Dependencies

```toml
[dependencies]
solana-stream-sdk = "0.2.5"
tokio = { version = "1", features = ["rt-multi-thread", "macros"] }
solana-entry = "2.2.1"          # For Entry deserialization
bincode = "1.3.3"               # For deserializing entry bytes
dotenvy = "0.15"                # For environment variables
```

## Complete Implementation Template

```rust
use solana_stream_sdk::{CommitmentLevel, ShredstreamClient};
use std::env;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Load environment variables
    dotenvy::dotenv().ok();

    // Connect to shredstream endpoint
    let endpoint = env::var("SHREDS_ENDPOINT")
        .unwrap_or_else(|_| "https://shreds-ams.erpc.global".to_string());

    let mut client = ShredstreamClient::connect(&endpoint).await?;

    // Create subscription request (example: Pump.fun program)
    let request = ShredstreamClient::create_entries_request_for_account(
        "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P",
        Some(CommitmentLevel::Processed),
    );

    // Subscribe to stream
    let mut stream = client.subscribe_entries(request).await?;

    // Process entries
    while let Some(slot_entry) = stream.message().await? {
        // Deserialize the entries bytes into Solana Entry structures
        let entries = match bincode::deserialize::<Vec<solana_entry::entry::Entry>>(&slot_entry.entries) {
            Ok(e) => e,
            Err(e) => {
                eprintln!("Deserialization failed: {}", e);
                continue;
            }
        };

        // Extract all transactions from entries
        let transactions: Vec<_> = entries
            .iter()
            .flat_map(|e| e.transactions.iter())
            .collect();

        println!(
            "Slot: {}, Entries: {}, Transactions: {}",
            slot_entry.slot,
            entries.len(),
            transactions.len()
        );

        // Process individual transactions
        for transaction in transactions {
            // Access transaction data: transaction.message, signatures, etc.
            println!("Processing transaction with {} instructions",
                transaction.message.instructions().len());
        }
    }

    Ok(())
}
```

## Advanced Filtering

### Multiple Accounts Subscription

```rust
let request = ShredstreamClient::create_entries_request_for_accounts(
    vec![
        "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P".to_string(), // Pump.fun
        "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM".to_string(), // Raydium
    ],
    vec![], // owners
    vec![], // custom filters
    Some(CommitmentLevel::Processed),
);
```

### Custom Request Building

```rust
use std::collections::HashMap;
use solana_stream_sdk::{
    SubscribeEntriesRequest, SubscribeRequestFilterAccounts,
    SubscribeRequestFilterSlots, SubscribeRequestFilterTransactions
};

let mut accounts = HashMap::new();
accounts.insert(
    "usdc-filter".to_string(),
    SubscribeRequestFilterAccounts {
        account: vec!["EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v".to_string()], // USDC
        owner: vec![],
        filters: vec![],
        nonempty_txn_signature: None,
    },
);

let request = SubscribeEntriesRequest {
    accounts,
    transactions: HashMap::new(),
    slots: HashMap::new(),
    commitment: Some(CommitmentLevel::Processed as i32),
};
```

## Error Handling

```rust
#[derive(Error, Debug)]
pub enum SolanaStreamError {
    Transport(#[from] tonic::transport::Error),    // Network errors
    Status(#[from] tonic::Status),                 // gRPC errors
    Serialization(String),                         // Data errors
    Connection(String),                            // Connection errors
    Configuration(String),                         // Config errors
}
```

### Robust Error Handling Pattern

```rust
while let Some(entry_result) = stream.message().await {
    match entry_result {
        Ok(entry) => {
            match bincode::deserialize::<Vec<solana_entry::entry::Entry>>(&entry.entries) {
                Ok(entries) => {
                    // Process entries successfully
                    for entry in entries {
                        // Handle entry data
                    }
                }
                Err(e) => {
                    log::warn!("Failed to deserialize entry: {}", e);
                    continue;
                }
            }
        }
        Err(e) => {
            log::error!("Stream error: {}", e);
            // Implement reconnection logic here
            break;
        }
    }
}
```

## Environment Configuration

### .env File

```env
SHREDS_ENDPOINT=https://shreds-ams.erpc.global
RUST_LOG=info
```

### Environment Variable Loading

```rust
use std::env;

// Load .env file
dotenvy::dotenv().ok();

// Get endpoint with fallback
let endpoint = env::var("SHREDS_ENDPOINT")
    .unwrap_or_else(|_| "https://shreds-ams.erpc.global".to_string());

// Enable logging
env_logger::init();
```

## Protocol Buffer Definitions

### Shredstream Service

```protobuf
service ShredstreamProxy {
    rpc SubscribeEntries(SubscribeEntriesRequest) returns (stream Entry);
}

message SubscribeEntriesRequest {
    map<string, SubscribeRequestFilterAccounts> accounts = 1;
    map<string, SubscribeRequestFilterTransactions> transactions = 3;
    map<string, SubscribeRequestFilterSlots> slots = 2;
    optional CommitmentLevel commitment = 6;
}

message Entry {
    uint64 slot = 1;           // Slot number where entry belongs
    bytes entries = 2;         // Serialized Vec<Entry> using bincode
}
```

## Performance Considerations

### Optimization Patterns

```rust
// Use bounded channels to prevent memory leaks
let (tx, mut rx) = tokio::sync::mpsc::channel(1000);

// Process entries in separate task
tokio::spawn(async move {
    while let Some(entry) = rx.recv().await {
        process_entry_async(entry).await;
    }
});

// Stream processing with backpressure handling
while let Some(entry) = stream.message().await? {
    if tx.try_send(entry).is_err() {
        log::warn!("Processing queue full, dropping entry");
    }
}
```

### Connection Management

```rust
async fn connect_with_retry(endpoint: &str, max_retries: u32) -> Result<ShredstreamClient> {
    for attempt in 0..max_retries {
        match ShredstreamClient::connect(endpoint).await {
            Ok(client) => return Ok(client),
            Err(e) if attempt == max_retries - 1 => return Err(e),
            Err(_) => {
                let delay = Duration::from_secs(2_u64.pow(attempt.min(6)));
                tokio::time::sleep(delay).await;
            }
        }
    }
    unreachable!()
}
```

## Common Use Cases

### 1. Pump.fun Token Monitoring

```rust
const PUMP_FUN_PROGRAM: &str = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P";

let request = ShredstreamClient::create_entries_request_for_account(
    PUMP_FUN_PROGRAM,
    Some(CommitmentLevel::Processed),
);
```

### 2. DEX Transaction Monitoring

```rust
let dex_programs = vec![
    "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM".to_string(), // Raydium
    "22Y43yTVxuUkoRKdm9thyRhQ3SdgQS7c7kB6UNCiaczD".to_string(), // Serum
];

let request = ShredstreamClient::create_entries_request_for_accounts(
    dex_programs,
    vec![],
    vec![],
    Some(CommitmentLevel::Processed),
);
```

### 3. Wallet Activity Tracking

```rust
let request = ShredstreamClient::create_entries_request_for_account(
    "YourWalletAddressHere",
    Some(CommitmentLevel::Confirmed),
);
```

## Build Configuration

### Cargo.toml for Projects Using SDK

```toml
[package]
name = "shreds-decoder"
version = "0.1.0"
edition = "2021"

[dependencies]
solana-stream-sdk = "0.2.5"
tokio = { version = "1", features = ["rt-multi-thread", "macros", "net", "io-util", "time"] }
solana-entry = "2.2.1"
bincode = "1.3.3"
dotenvy = "0.15"
log = "0.4"
env_logger = "0.11"
thiserror = "1"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
```

## Troubleshooting Guide

### Common Issues and Solutions

1. **Connection Timeout**

    ```rust
    // Add timeout configuration
    let client = ShredstreamClient::connect(&endpoint).await
        .map_err(|e| format!("Connection failed: {}", e))?;
    ```

2. **Deserialization Errors**

    ```rust
    match bincode::deserialize::<Vec<solana_entry::entry::Entry>>(&entry.entries) {
        Ok(entries) => entries,
        Err(e) => {
            log::error!("Deserialization failed: {}", e);
            continue; // Skip malformed entries
        }
    }
    ```

3. **Stream Disconnection Handling**
    ```rust
    loop {
        match connect_and_stream(&endpoint).await {
            Ok(_) => break,
            Err(e) => {
                log::error!("Stream error: {}, reconnecting...", e);
                tokio::time::sleep(Duration::from_secs(5)).await;
            }
        }
    }
    ```

## Key Points for AI Assistants

1. **Shreds are serialized**: Always use `bincode::deserialize` to convert `entry.entries` bytes into `Vec<solana_entry::entry::Entry>`

2. **Async Pattern**: All operations are async, use `tokio::main` and `.await`

3. **Error Handling**: Implement proper error handling for network issues and data corruption

4. **Commitment Levels**: Use `PROCESSED` for lowest latency, `CONFIRMED` for balance, `FINALIZED` for highest confidence

5. **Resource Management**: Use bounded channels and proper connection management to prevent memory leaks

6. **Environment Variables**: Always use environment variables for endpoints and tokens

7. **Filtering**: Use appropriate filters to reduce bandwidth and processing overhead

This documentation provides all essential context for implementing Solana shred streaming applications using the Solana Stream SDK.
