use serde::{Deserialize, Serialize};

use crate::types::{ConfigError, ConfigResult};

/// Main application configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    /// Application name
    pub name: String,
    
    /// Application version
    pub version: String,
    
    /// Environment (development, staging, production)
    pub environment: Environment,
    
    /// Application bind address
    pub bind_address: String,
    
    /// Application port
    pub port: u16,
    
    /// Maximum number of concurrent connections
    pub max_connections: usize,
    
    /// Request timeout in milliseconds
    pub request_timeout_ms: u64,
    
    /// Enable metrics collection
    pub enable_metrics: bool,
    
    /// Metrics port (if metrics enabled)
    pub metrics_port: Option<u16>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum Environment {
    Development,
    Staging,
    Production,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            name: "shreds-decoder".to_string(),
            version: env!("CARGO_PKG_VERSION").to_string(),
            environment: Environment::Development,
            bind_address: "0.0.0.0".to_string(),
            port: 8080,
            max_connections: 1000,
            request_timeout_ms: 5000,
            enable_metrics: false,
            metrics_port: None,
        }
    }
}

impl AppConfig {
    pub fn validate(&self) -> ConfigResult<()> {
        if self.name.is_empty() {
            return Err(ConfigError::InvalidValue {
                key: "name".to_string(),
                value: self.name.clone(),
            });
        }

        if self.port == 0 {
            return Err(ConfigError::InvalidValue {
                key: "port".to_string(),
                value: self.port.to_string(),
            });
        }

        if self.max_connections == 0 {
            return Err(ConfigError::InvalidValue {
                key: "max_connections".to_string(),
                value: self.max_connections.to_string(),
            });
        }

        if self.request_timeout_ms == 0 {
            return Err(ConfigError::InvalidValue {
                key: "request_timeout_ms".to_string(),
                value: self.request_timeout_ms.to_string(),
            });
        }

        if self.enable_metrics && self.metrics_port.is_none() {
            return Err(ConfigError::MissingRequired {
                key: "metrics_port".to_string(),
            });
        }

        Ok(())
    }

    pub fn is_production(&self) -> bool {
        matches!(self.environment, Environment::Production)
    }

    pub fn is_development(&self) -> bool {
        matches!(self.environment, Environment::Development)
    }
}
