use serde::{Deserialize, Serialize};

use crate::types::{ConfigError, ConfigResult};

/// Logging configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    /// Global log level
    pub level: LogLevel,

    /// Log format (json, pretty, compact)
    pub format: LogFormat,

    /// Enable colored output (only for pretty format)
    pub enable_colors: bool,

    /// Enable timestamp in logs
    pub enable_timestamp: bool,

    /// Enable source location (file:line) in logs
    pub enable_source_location: bool,

    /// Enable thread ID in logs
    pub enable_thread_id: bool,

    /// Enable span events
    pub enable_span_events: bool,

    /// Log file path (optional, if None logs to stdout)
    pub file_path: Option<String>,

    /// Maximum log file size in bytes before rotation
    pub max_file_size: Option<u64>,

    /// Maximum number of rotated log files to keep
    pub max_files: Option<u32>,

    /// Module-specific log levels
    #[serde(default)]
    pub module_levels: std::collections::HashMap<String, LogLevel>,

    /// Enable performance metrics logging
    pub enable_metrics: bool,

    /// Metrics logging interval in seconds
    pub metrics_interval_secs: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum LogLevel {
    Trace,
    Debug,
    Info,
    Warn,
    Error,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum LogFormat {
    Json,
    Pretty,
    Compact,
}

impl Default for LoggingConfig {
    fn default() -> Self {
        Self {
            level: LogLevel::Info,
            format: LogFormat::Pretty,
            enable_colors: true,
            enable_timestamp: true,
            enable_source_location: false,
            enable_thread_id: false,
            enable_span_events: true,
            file_path: None,
            max_file_size: Some(100 * 1024 * 1024), // 100MB
            max_files: Some(5),
            module_levels: std::collections::HashMap::new(),
            enable_metrics: true,
            metrics_interval_secs: 60,
        }
    }
}

impl LoggingConfig {
    pub fn validate(&self) -> ConfigResult<()> {
        if let Some(ref file_path) = self.file_path {
            if file_path.is_empty() {
                return Err(ConfigError::InvalidValue {
                    key: "logging.file_path".to_string(),
                    value: file_path.clone(),
                });
            }
        }

        if let Some(max_file_size) = self.max_file_size {
            if max_file_size == 0 {
                return Err(ConfigError::InvalidValue {
                    key: "logging.max_file_size".to_string(),
                    value: max_file_size.to_string(),
                });
            }
        }

        if let Some(max_files) = self.max_files {
            if max_files == 0 {
                return Err(ConfigError::InvalidValue {
                    key: "logging.max_files".to_string(),
                    value: max_files.to_string(),
                });
            }
        }

        if self.metrics_interval_secs == 0 {
            return Err(ConfigError::InvalidValue {
                key: "logging.metrics_interval_secs".to_string(),
                value: self.metrics_interval_secs.to_string(),
            });
        }

        Ok(())
    }

    pub fn is_json_format(&self) -> bool {
        matches!(self.format, LogFormat::Json)
    }

    pub fn is_pretty_format(&self) -> bool {
        matches!(self.format, LogFormat::Pretty)
    }

    pub fn should_use_colors(&self) -> bool {
        self.enable_colors && self.is_pretty_format()
    }
}

impl From<LogLevel> for tracing::Level {
    fn from(level: LogLevel) -> Self {
        match level {
            LogLevel::Trace => tracing::Level::TRACE,
            LogLevel::Debug => tracing::Level::DEBUG,
            LogLevel::Info => tracing::Level::INFO,
            LogLevel::Warn => tracing::Level::WARN,
            LogLevel::Error => tracing::Level::ERROR,
        }
    }
}
