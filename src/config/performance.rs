use serde::{Deserialize, Serialize};

use crate::types::{ConfigError, ConfigR<PERSON>ult};

/// Performance tuning configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    /// Number of worker threads for async runtime
    pub worker_threads: Option<usize>,

    /// Enable thread affinity (pin threads to CPU cores)
    pub enable_thread_affinity: bool,

    /// CPU cores to use (if thread affinity enabled)
    pub cpu_cores: Option<Vec<usize>>,

    /// Buffer pool size for memory reuse
    pub buffer_pool_size: usize,

    /// Initial buffer size in bytes
    pub initial_buffer_size: usize,

    /// Maximum buffer size in bytes
    pub max_buffer_size: usize,

    /// Enable zero-copy optimizations
    pub enable_zero_copy: bool,

    /// Batch size for processing multiple items
    pub batch_size: usize,

    /// Batch timeout in milliseconds
    pub batch_timeout_ms: u64,

    /// Enable lock-free data structures where possible
    pub enable_lock_free: bool,

    /// Channel buffer size for inter-task communication
    pub channel_buffer_size: usize,

    /// Enable memory prefetching
    pub enable_prefetch: bool,

    /// Memory allocation strategy
    pub allocation_strategy: AllocationStrategy,

    /// Enable CPU-specific optimizations
    pub enable_cpu_optimizations: bool,

    /// Target latency in microseconds
    pub target_latency_us: u64,

    /// Enable latency monitoring
    pub enable_latency_monitoring: bool,

    /// Latency percentiles to track
    pub latency_percentiles: Vec<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum AllocationStrategy {
    /// Use system allocator
    System,
    /// Use memory pools
    Pool,
    /// Use arena allocation
    Arena,
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            worker_threads: None, // Use default (number of CPU cores)
            enable_thread_affinity: false,
            cpu_cores: None,
            buffer_pool_size: 1024,
            initial_buffer_size: 8192,
            max_buffer_size: 1024 * 1024, // 1MB
            enable_zero_copy: true,
            batch_size: 100,
            batch_timeout_ms: 10,
            enable_lock_free: true,
            channel_buffer_size: 1024,
            enable_prefetch: true,
            allocation_strategy: AllocationStrategy::Pool,
            enable_cpu_optimizations: true,
            target_latency_us: 1000, // 1ms
            enable_latency_monitoring: true,
            latency_percentiles: vec![50.0, 90.0, 95.0, 99.0, 99.9],
        }
    }
}

impl PerformanceConfig {
    pub fn validate(&self) -> ConfigResult<()> {
        if let Some(worker_threads) = self.worker_threads {
            if worker_threads == 0 {
                return Err(ConfigError::InvalidValue {
                    key: "performance.worker_threads".to_string(),
                    value: worker_threads.to_string(),
                });
            }
        }

        if self.buffer_pool_size == 0 {
            return Err(ConfigError::InvalidValue {
                key: "performance.buffer_pool_size".to_string(),
                value: self.buffer_pool_size.to_string(),
            });
        }

        if self.initial_buffer_size == 0 {
            return Err(ConfigError::InvalidValue {
                key: "performance.initial_buffer_size".to_string(),
                value: self.initial_buffer_size.to_string(),
            });
        }

        if self.max_buffer_size < self.initial_buffer_size {
            return Err(ConfigError::InvalidValue {
                key: "performance.max_buffer_size".to_string(),
                value: format!(
                    "max_buffer_size ({}) must be >= initial_buffer_size ({})",
                    self.max_buffer_size, self.initial_buffer_size
                ),
            });
        }

        if self.batch_size == 0 {
            return Err(ConfigError::InvalidValue {
                key: "performance.batch_size".to_string(),
                value: self.batch_size.to_string(),
            });
        }

        if self.channel_buffer_size == 0 {
            return Err(ConfigError::InvalidValue {
                key: "performance.channel_buffer_size".to_string(),
                value: self.channel_buffer_size.to_string(),
            });
        }

        if self.target_latency_us == 0 {
            return Err(ConfigError::InvalidValue {
                key: "performance.target_latency_us".to_string(),
                value: self.target_latency_us.to_string(),
            });
        }

        for percentile in &self.latency_percentiles {
            if *percentile <= 0.0 || *percentile >= 100.0 {
                return Err(ConfigError::InvalidValue {
                    key: "performance.latency_percentiles".to_string(),
                    value: percentile.to_string(),
                });
            }
        }

        Ok(())
    }

    pub fn get_worker_threads(&self) -> usize {
        self.worker_threads.unwrap_or_else(get_cpu_count)
    }

    pub fn is_sub_millisecond_target(&self) -> bool {
        self.target_latency_us < 1000
    }
}

// Helper function to get number of CPU cores
fn get_cpu_count() -> usize {
    std::thread::available_parallelism().map(|n| n.get()).unwrap_or(1)
}
