use config::{Config, Environment, File};
use serde::{Deserialize, Serialize};
use std::env;

use crate::types::{ConfigError, ConfigResult};

pub mod app;
pub mod erpc;
pub mod grpc;
pub mod logging;
pub mod performance;

pub use app::AppConfig;
pub use erpc::ErpcConfig;
pub use grpc::GrpcConfig;
pub use logging::LoggingConfig;
pub use performance::PerformanceConfig;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MainConfig {
    pub app: AppConfig,
    pub erpc: ErpcConfig,
    pub grpc: GrpcConfig,
    pub logging: LoggingConfig,
    pub performance: PerformanceConfig,
}

impl Default for MainConfig {
    fn default() -> Self {
        Self {
            app: AppConfig::default(),
            erpc: ErpcConfig::default(),
            grpc: GrpcConfig::default(),
            logging: LoggingConfig::default(),
            performance: PerformanceConfig::default(),
        }
    }
}

impl MainConfig {
    pub fn load() -> ConfigResult<Self> {
        let mut builder = Config::builder()
            // Start with default values
            .add_source(Config::try_from(&MainConfig::default())?);

        // Add configuration file if it exists
        let config_file = env::var("CONFIG_FILE").unwrap_or_else(|_| "config".to_string());
        builder = builder.add_source(File::with_name(&config_file).required(false));

        // Add environment variables with prefix
        builder = builder.add_source(Environment::with_prefix("SHREDS").prefix_separator("_").separator("__"));

        let config = builder.build().map_err(|e| ConfigError::ParseError { source: e })?;
        let main_config: MainConfig = config
            .try_deserialize()
            .map_err(|e| ConfigError::ParseError { source: e })?;

        // Validate all configurations
        main_config.validate()?;

        Ok(main_config)
    }

    pub fn validate(&self) -> ConfigResult<()> {
        self.app.validate()?;
        self.erpc.validate()?;
        self.grpc.validate()?;
        self.logging.validate()?;
        self.performance.validate()?;
        Ok(())
    }

    pub fn to_json(&self) -> ConfigResult<String> {
        serde_json::to_string_pretty(self).map_err(|e| ConfigError::InvalidValue {
            key: "config_serialization".to_string(),
            value: e.to_string(),
        })
    }
}
