use anyhow::{Ok, Result};
use config::{Config, File};
use serde::{Deserialize, Serialize};

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct AppConfig {}

impl AppConfig {
    pub fn new() -> Result<Self> {
        let builder = Config::builder()
            .add_source(Config::try_from(&AppConfig::default())?)
            .add_source(File::with_name("config"));

        let config = builder.build()?;
        let app_config = config.try_deserialize()?;

        Ok(app_config)
    }
}
