use serde::{Deserialize, Serialize};
use std::time::Duration;

use crate::types::{ConfigError, ConfigR<PERSON>ult};

/// ERPC client configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ErpcConfig {
    /// ERPC endpoint URL
    pub endpoint: String,
    
    /// Authentication token
    pub token: Option<String>,
    
    /// Connection timeout in milliseconds
    pub connection_timeout_ms: u64,
    
    /// Request timeout in milliseconds
    pub request_timeout_ms: u64,
    
    /// Keep-alive interval in milliseconds
    pub keep_alive_interval_ms: u64,
    
    /// Maximum number of retry attempts
    pub max_retries: u32,
    
    /// Initial retry delay in milliseconds
    pub initial_retry_delay_ms: u64,
    
    /// Maximum retry delay in milliseconds
    pub max_retry_delay_ms: u64,
    
    /// Retry backoff multiplier
    pub retry_backoff_multiplier: f64,
    
    /// Enable automatic reconnection
    pub auto_reconnect: bool,
    
    /// Maximum number of concurrent subscriptions
    pub max_subscriptions: usize,
    
    /// Buffer size for incoming shreds
    pub buffer_size: usize,
    
    /// Enable compression
    pub enable_compression: bool,
}

impl Default for ErpcConfig {
    fn default() -> Self {
        Self {
            endpoint: "https://shreds-ams.erpc.global".to_string(),
            token: None,
            connection_timeout_ms: 10000,
            request_timeout_ms: 5000,
            keep_alive_interval_ms: 30000,
            max_retries: 5,
            initial_retry_delay_ms: 1000,
            max_retry_delay_ms: 30000,
            retry_backoff_multiplier: 2.0,
            auto_reconnect: true,
            max_subscriptions: 100,
            buffer_size: 8192,
            enable_compression: true,
        }
    }
}

impl ErpcConfig {
    pub fn validate(&self) -> ConfigResult<()> {
        if self.endpoint.is_empty() {
            return Err(ConfigError::MissingRequired {
                key: "erpc.endpoint".to_string(),
            });
        }

        if !self.endpoint.starts_with("http://") && !self.endpoint.starts_with("https://") {
            return Err(ConfigError::InvalidValue {
                key: "erpc.endpoint".to_string(),
                value: self.endpoint.clone(),
            });
        }

        if self.connection_timeout_ms == 0 {
            return Err(ConfigError::InvalidValue {
                key: "erpc.connection_timeout_ms".to_string(),
                value: self.connection_timeout_ms.to_string(),
            });
        }

        if self.max_retries == 0 {
            return Err(ConfigError::InvalidValue {
                key: "erpc.max_retries".to_string(),
                value: self.max_retries.to_string(),
            });
        }

        if self.retry_backoff_multiplier <= 1.0 {
            return Err(ConfigError::InvalidValue {
                key: "erpc.retry_backoff_multiplier".to_string(),
                value: self.retry_backoff_multiplier.to_string(),
            });
        }

        if self.max_subscriptions == 0 {
            return Err(ConfigError::InvalidValue {
                key: "erpc.max_subscriptions".to_string(),
                value: self.max_subscriptions.to_string(),
            });
        }

        if self.buffer_size == 0 {
            return Err(ConfigError::InvalidValue {
                key: "erpc.buffer_size".to_string(),
                value: self.buffer_size.to_string(),
            });
        }

        Ok(())
    }

    pub fn connection_timeout(&self) -> Duration {
        Duration::from_millis(self.connection_timeout_ms)
    }

    pub fn request_timeout(&self) -> Duration {
        Duration::from_millis(self.request_timeout_ms)
    }

    pub fn keep_alive_interval(&self) -> Duration {
        Duration::from_millis(self.keep_alive_interval_ms)
    }

    pub fn initial_retry_delay(&self) -> Duration {
        Duration::from_millis(self.initial_retry_delay_ms)
    }

    pub fn max_retry_delay(&self) -> Duration {
        Duration::from_millis(self.max_retry_delay_ms)
    }
}
