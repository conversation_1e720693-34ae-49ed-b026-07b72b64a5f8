use serde::{Deserialize, Serialize};
use std::time::Duration;

use crate::types::{ConfigError, ConfigResult};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GrpcConfig {
    pub bind_address: String,

    pub port: u16,

    pub enable_tls: bool,

    pub tls_cert_path: Option<String>,

    pub tls_key_path: Option<String>,

    pub max_connections: usize,

    pub connection_timeout_ms: u64,

    pub keep_alive_timeout_ms: u64,

    pub keep_alive_interval_ms: u64,

    pub tcp_nodelay: bool,

    pub max_message_size: usize,

    pub stream_buffer_size: usize,

    pub enable_compression: bool,

    pub max_concurrent_streams: u32,

    pub enable_reflection: bool,

    pub enable_health_check: bool,
}

impl Default for GrpcConfig {
    fn default() -> Self {
        Self {
            bind_address: "127.0.0.1".to_string(),
            port: 50051,
            enable_tls: false, // Disabled for maximum performance
            tls_cert_path: None,
            tls_key_path: None,
            max_connections: 1000,
            connection_timeout_ms: 10000,
            keep_alive_timeout_ms: 60000,
            keep_alive_interval_ms: 30000,
            tcp_nodelay: true,                 // Enable for reduced latency
            max_message_size: 4 * 1024 * 1024, // 4MB
            stream_buffer_size: 8192,
            enable_compression: false, // Disabled for performance
            max_concurrent_streams: 100,
            enable_reflection: false,
            enable_health_check: true,
        }
    }
}

impl GrpcConfig {
    pub fn validate(&self) -> ConfigResult<()> {
        if self.bind_address.is_empty() {
            return Err(ConfigError::InvalidValue {
                key: "grpc.bind_address".to_string(),
                value: self.bind_address.clone(),
            });
        }

        if self.port == 0 {
            return Err(ConfigError::InvalidValue {
                key: "grpc.port".to_string(),
                value: self.port.to_string(),
            });
        }

        if self.enable_tls {
            if self.tls_cert_path.is_none() {
                return Err(ConfigError::MissingRequired {
                    key: "grpc.tls_cert_path".to_string(),
                });
            }
            if self.tls_key_path.is_none() {
                return Err(ConfigError::MissingRequired {
                    key: "grpc.tls_key_path".to_string(),
                });
            }
        }

        if self.max_connections == 0 {
            return Err(ConfigError::InvalidValue {
                key: "grpc.max_connections".to_string(),
                value: self.max_connections.to_string(),
            });
        }

        if self.max_message_size == 0 {
            return Err(ConfigError::InvalidValue {
                key: "grpc.max_message_size".to_string(),
                value: self.max_message_size.to_string(),
            });
        }

        if self.max_concurrent_streams == 0 {
            return Err(ConfigError::InvalidValue {
                key: "grpc.max_concurrent_streams".to_string(),
                value: self.max_concurrent_streams.to_string(),
            });
        }

        Ok(())
    }

    pub fn connection_timeout(&self) -> Duration {
        Duration::from_millis(self.connection_timeout_ms)
    }

    pub fn keep_alive_timeout(&self) -> Duration {
        Duration::from_millis(self.keep_alive_timeout_ms)
    }

    pub fn keep_alive_interval(&self) -> Duration {
        Duration::from_millis(self.keep_alive_interval_ms)
    }

    pub fn server_address(&self) -> String {
        format!("{}:{}", self.bind_address, self.port)
    }
}
