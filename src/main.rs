use anyhow::Result;

mod config;
mod types;
mod utils;

use config::MainConfig;
use utils::{init_logging, log_startup_info};

fn main() -> Result<()> {
    // Load configuration first
    let config = MainConfig::load().map_err(|e| anyhow::anyhow!("Failed to load config: {}", e))?;

    // Initialize logging system
    init_logging(&config.logging).map_err(|e| anyhow::anyhow!("Failed to initialize logging: {}", e))?;

    // Log startup information
    log_startup_info(&config);

    tracing::info!("Configuration loaded successfully!");
    tracing::info!(app_name = %config.app.name, "Application initialized");

    Ok(())
}
