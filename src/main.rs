use anyhow::Result;

mod client;
mod config;
mod types;
mod utils;

use client::ErpcClient;
use config::MainConfig;
use utils::{init_logging, log_startup_info};

#[tokio::main]
async fn main() -> Result<()> {
    let config = MainConfig::load().map_err(|e| anyhow::anyhow!("Failed to load config: {}", e))?;

    init_logging(&config.logging).map_err(|e| anyhow::anyhow!("Failed to initialize logging: {}", e))?;

    log_startup_info(&config);

    tracing::info!("Configuration loaded successfully!");
    tracing::info!(app_name = %config.app.name, "Application initialized");

    let test_accounts = get_test_accounts();
    tracing::info!(accounts = ?test_accounts, "Using test accounts for monitoring");

    let mut erpc_client = ErpcClient::builder(config.erpc.clone())
        .build()
        .await
        .map_err(|e| anyhow::anyhow!("Failed to create ERPC client: {}", e))?;

    tracing::info!("ERPC client created successfully, starting subscription...");

    if let Err(e) = erpc_client.run_with_initial_accounts(test_accounts).await {
        tracing::error!(error = %e, "ERPC client failed");
        return Err(anyhow::anyhow!("ERPC client error: {}", e));
    }

    Ok(())
}

fn get_test_accounts() -> Vec<String> {
    vec![
        "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P".to_string(),
        "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM".to_string(),
        "22Y43yTVxuUkoRKdm9thyRhQ3SdgQS7c7kB6UNCiaczD".to_string(),
        "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v".to_string(),
        "So11111111111111111111111111111111111111112".to_string(),
    ]
}
