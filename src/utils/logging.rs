use tracing_subscriber::fmt;

use crate::config::LoggingConfig;
use crate::types::{ConfigError, ConfigResult};

pub fn init_logging(config: &LoggingConfig) -> ConfigResult<()> {
    // Initialize based on format preference
    match config.format {
        crate::config::logging::LogFormat::Json => {
            let subscriber = fmt().json().with_max_level(convert_log_level(&config.level)).finish();
            tracing::subscriber::set_global_default(subscriber)
        }
        crate::config::logging::LogFormat::Pretty => {
            let subscriber = fmt().pretty().with_max_level(convert_log_level(&config.level)).finish();
            tracing::subscriber::set_global_default(subscriber)
        }
        crate::config::logging::LogFormat::Compact => {
            let subscriber = fmt()
                .compact()
                .with_max_level(convert_log_level(&config.level))
                .finish();
            tracing::subscriber::set_global_default(subscriber)
        }
    }
    .map_err(|e| ConfigError::InvalidValue {
        key: "logging.init".to_string(),
        value: e.to_string(),
    })?;

    Ok(())
}

fn convert_log_level(level: &crate::config::logging::LogLevel) -> tracing::Level {
    match level {
        crate::config::logging::LogLevel::Trace => tracing::Level::TRACE,
        crate::config::logging::LogLevel::Debug => tracing::Level::DEBUG,
        crate::config::logging::LogLevel::Info => tracing::Level::INFO,
        crate::config::logging::LogLevel::Warn => tracing::Level::WARN,
        crate::config::logging::LogLevel::Error => tracing::Level::ERROR,
    }
}

pub fn log_startup_info(config: &crate::config::MainConfig) {
    tracing::info!(
        app_name = %config.app.name,
        app_version = %config.app.version,
        environment = ?config.app.environment,
        grpc_address = %format!("{}:{}", config.grpc.bind_address, config.grpc.port),
        erpc_endpoint = %config.erpc.endpoint,
        "Application starting"
    );
}

pub fn log_performance_metrics(
    latency_us: u64,
    throughput_ops_per_sec: f64,
    memory_usage_mb: f64,
    cpu_usage_percent: f64,
) {
    tracing::info!(
        latency_us = latency_us,
        throughput_ops_per_sec = throughput_ops_per_sec,
        memory_usage_mb = memory_usage_mb,
        cpu_usage_percent = cpu_usage_percent,
        "Performance metrics"
    );
}

pub fn log_error_with_context(error: &dyn std::error::Error, context: &str) {
    tracing::error!(
        error = %error,
        context = context,
        "Error occurred"
    );
}

pub fn create_operation_span(operation: &str, account: Option<&str>) -> tracing::Span {
    if let Some(account) = account {
        tracing::info_span!("operation", op = operation, account = account)
    } else {
        tracing::info_span!("operation", op = operation)
    }
}
