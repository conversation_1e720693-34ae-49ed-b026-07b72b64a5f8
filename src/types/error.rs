use thiserror::Error;

#[derive(Error, Debug)]
pub enum ShredsError {
    #[error("Configuration error: {message}")]
    Config { message: String },

    #[error("ERPC client error: {message}")]
    ErpcClient { message: String },

    #[error("Connection error: {message}")]
    Connection { message: String },

    #[error("Shred decoding error: {message}")]
    ShredDecoding { message: String },

    #[error("gRPC server error: {message}")]
    GrpcServer { message: String },

    #[error("Serialization error: {message}")]
    Serialization { message: String },

    #[error("Validation error: {message}")]
    Validation { message: String },

    #[error("IO error: {source}")]
    Io {
        #[from]
        source: std::io::Error,
    },

    #[error("Network timeout: operation timed out after {timeout_ms}ms")]
    Timeout { timeout_ms: u64 },

    #[error("Authentication failed: {message}")]
    Authentication { message: String },

    #[error("Rate limit exceeded: {message}")]
    RateLimit { message: String },

    #[error("Internal error: {message}")]
    Internal { message: String },
}

impl ShredsError {
    pub fn config<T: Into<String>>(message: T) -> Self {
        Self::Config {
            message: message.into(),
        }
    }

    pub fn erpc_client<T: Into<String>>(message: T) -> Self {
        Self::ErpcClient {
            message: message.into(),
        }
    }

    pub fn connection<T: Into<String>>(message: T) -> Self {
        Self::Connection {
            message: message.into(),
        }
    }

    pub fn shred_decoding<T: Into<String>>(message: T) -> Self {
        Self::ShredDecoding {
            message: message.into(),
        }
    }

    pub fn grpc_server<T: Into<String>>(message: T) -> Self {
        Self::GrpcServer {
            message: message.into(),
        }
    }

    pub fn serialization<T: Into<String>>(message: T) -> Self {
        Self::Serialization {
            message: message.into(),
        }
    }

    pub fn validation<T: Into<String>>(message: T) -> Self {
        Self::Validation {
            message: message.into(),
        }
    }

    pub fn timeout(timeout_ms: u64) -> Self {
        Self::Timeout { timeout_ms }
    }

    pub fn authentication<T: Into<String>>(message: T) -> Self {
        Self::Authentication {
            message: message.into(),
        }
    }

    pub fn rate_limit<T: Into<String>>(message: T) -> Self {
        Self::RateLimit {
            message: message.into(),
        }
    }

    pub fn internal<T: Into<String>>(message: T) -> Self {
        Self::Internal {
            message: message.into(),
        }
    }
}

#[derive(Error, Debug)]
pub enum ConfigError {
    #[error("Missing required configuration: {key}")]
    MissingRequired { key: String },

    #[error("Invalid configuration value for {key}: {value}")]
    InvalidValue { key: String, value: String },

    #[error("Configuration file not found: {path}")]
    FileNotFound { path: String },

    #[error("Failed to parse configuration: {source}")]
    ParseError {
        #[from]
        source: config::ConfigError,
    },

    #[error("Environment variable error: {message}")]
    Environment { message: String },
}

#[derive(Error, Debug)]
pub enum ErpcError {
    #[error("Connection failed: {message}")]
    ConnectionFailed { message: String },

    #[error("Authentication failed: {message}")]
    AuthenticationFailed { message: String },

    #[error("Subscription failed: {message}")]
    SubscriptionFailed { message: String },

    #[error("Stream disconnected: {message}")]
    StreamDisconnected { message: String },

    #[error("Invalid response: {message}")]
    InvalidResponse { message: String },
}

#[derive(Error, Debug)]
pub enum GrpcError {
    #[error("Server startup failed: {message}")]
    StartupFailed { message: String },

    #[error("Client connection error: {message}")]
    ClientConnection { message: String },

    #[error("Stream error: {message}")]
    Stream { message: String },

    #[error("Protocol error: {message}")]
    Protocol { message: String },
}
