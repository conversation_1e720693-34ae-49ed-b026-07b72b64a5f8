use crate::types::error::{Config<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>hredsError};

pub type Result<T> = std::result::Result<T, ShredsError>;
pub type ConfigResult<T> = std::result::Result<T, ConfigError>;
pub type ErpcResult<T> = std::result::Result<T, ErpcError>;
pub type GrpcResult<T> = std::result::Result<T, GrpcError>;
pub type AnyhowResult<T> = anyhow::Result<T>;
