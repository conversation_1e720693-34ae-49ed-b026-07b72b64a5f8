use crate::types::error::{Config<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>hredsError};

/// Main result type for the application
pub type Result<T> = std::result::Result<T, ShredsError>;

/// Configuration-specific result type
pub type ConfigResult<T> = std::result::Result<T, ConfigError>;

/// ERPC client specific result type
pub type ErpcResult<T> = std::result::Result<T, ErpcError>;

/// gRPC server specific result type
pub type GrpcResult<T> = std::result::Result<T, GrpcError>;

/// Anyhow result type for general error handling
pub type AnyhowResult<T> = anyhow::Result<T>;
